import { useState, useEffect } from "react";
import { But<PERSON> } from "../components/ui/button";
import { Input } from "../components/ui/input";
import { ScrollArea } from "../components/ui/scroll-area";
import { Avatar, AvatarFallback } from "../components/ui/avatar";
import { Card, CardContent } from "../components/ui/card";
import { Badge } from "../components/ui/badge";
import {
  Plus,
  ShoppingCart,
  ThumbsUp,
  ThumbsDown,
  Paperclip,
  Mic,
  Send,
  MessageSquare,
  LogOut,
  ShoppingBag,
  X,
  Trash2,
} from "lucide-react";

interface Product {
  id: number;
  name: string;
  price: number;
}

interface CartItem {
  id: number;
  name: string;
  price: number;
  color: string;
  size: string;
}

interface ChatMessage {
  id: string;
  type: "user" | "assistant";
  content: string;
  products?: Product[];
  customization?: {
    colors: string[];
    selectedColor: string;
    sizes: string[];
    selectedSize: string | null;
  };
}

interface ChatHistory {
  id: string;
  title: string;
  timestamp: string;
}

export default function ShoppingAssistant() {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: "1",
      type: "user",
      content: "Show me casual Nike shoes",
    },
    {
      id: "2",
      type: "assistant",
      content: "Here's a list of products under casual Nike shoes",
      products: [
        {
          id: 1,
          name: "Air Force 1 Jester XX Black Sonic Yellow Shoes",
          price: 102.0,
        },
        { id: 2, name: "Air Jordan 1 Retro High Obsidian UNC", price: 120.0 },
        {
          id: 3,
          name: "Nike Men's Pegasus EasyOn Road Running Shoes",
          price: 98.0,
        },
      ],
    },
    {
      id: "3",
      type: "user",
      content:
        "Add item to Cart: Air Force 1 Jester XX Black Sonic Yellow Shoes",
    },
    {
      id: "4",
      type: "assistant",
      content: "Choose size and colour:",
      customization: {
        colors: ["Black", "Grey", "White", "Red", "Blue"],
        selectedColor: "Blue",
        sizes: ["6", "7", "8", "9", "10"],
        selectedSize: null,
      },
    },
  ]);

  const [inputValue, setInputValue] = useState("");
  const [cartCount, setCartCount] = useState(2);
  const [selectedColor, setSelectedColor] = useState("Blue");
  const [selectedSize, setSelectedSize] = useState<string | null>(null);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [isCartVisible, setIsCartVisible] = useState(false);
  const [cartItems, setCartItems] = useState<CartItem[]>([
    {
      id: 1,
      name: "Air Force 1 Jester XX Black Sonic Yellow Shoes",
      price: 102.0,
      color: "Blue",
      size: "8",
    },
    {
      id: 2,
      name: "Nike Men's Pegasus EasyOn Road Running Shoes",
      price: 98.0,
      color: "Blue",
      size: "8",
    },
  ]);

  const chatHistory: ChatHistory[] = [
    { id: "1", title: "Show me casual Nike sh...", timestamp: "current" },
    {
      id: "2",
      title: "What is your best selling...",
      timestamp: "2 hours ago",
    },
    { id: "3", title: "Laptops under $1500", timestamp: "5 hours ago" },
    { id: "4", title: "Waterproof shoes unde...", timestamp: "Jan 3, 2024" },
    { id: "5", title: "Shirts for men", timestamp: "Dec 27, 2023" },
  ];

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      type: "user",
      content: inputValue,
    };

    setMessages((prev) => [...prev, newMessage]);
    setInputValue("");

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: "assistant",
        content: "I'm searching for products that match your request...",
      };
      setMessages((prev) => [...prev, aiResponse]);
    }, 1000);
  };

  const handleAddToCart = (product: Product) => {
    setCartCount((prev) => prev + 1);
  };

  const handleProceed = () => {
    setIsCartVisible(true);
    setTimeout(() => setIsCartOpen(true), 10);
  };

  const handleCloseCart = () => {
    setIsCartOpen(false);
    setTimeout(() => setIsCartVisible(false), 300);
  };

  const handleRemoveFromCart = (itemId: number) => {
    setCartItems((prev) => prev.filter((item) => item.id !== itemId));
    setCartCount((prev) => prev - 1);
  };

  const handleClearCart = () => {
    setCartItems([]);
    setCartCount(0);
  };

  const getTotalPrice = () => {
    return cartItems.reduce((total, item) => total + item.price, 0);
  };

  // Handle escape key to close cart
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isCartVisible) {
        handleCloseCart();
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, [isCartVisible]);

  return (
    <div className="flex h-screen bg-white relative">
      {/* Sidebar */}
      <div
        className="w-64 flex flex-col"
        style={{ backgroundColor: "#2C2C2C" }}
      >
        {/* Header */}
        <div className="p-4" style={{ borderBottom: "1px solid #404040" }}>
          <div className="flex items-center gap-2 mb-4">
            <div
              className="w-6 h-6 rounded flex items-center justify-center"
              style={{
                background: "linear-gradient(180deg, #56917D 0%, #9EB998 100%)",
              }}
            >
              <ShoppingBag className="w-4 h-4 text-white" />
            </div>
            <span className="font-semibold text-white">Shopping Assistant</span>
          </div>

          <Button
            className="w-full text-white border-0 hover:opacity-90"
            style={{
              background: "linear-gradient(180deg, #56917D 0%, #9EB998 100%)",
            }}
          >
            <Plus className="w-4 h-4 mr-2" />
            New Chat
          </Button>
        </div>

        {/* Recent Chats */}
        <div className="flex-1 p-4">
          <h3 className="text-sm font-medium text-gray-400 mb-3">
            Recent Chats
          </h3>
          <ScrollArea className="space-y-2">
            {chatHistory.map((chat) => (
              <div
                key={chat.id}
                className={`flex items-center gap-2 p-2 rounded cursor-pointer hover:bg-gray-600 ${
                  chat.timestamp === "current" ? "bg-gray-600" : ""
                }`}
              >
                <MessageSquare className="w-4 h-4 text-gray-400" />
                <div className="flex-1 min-w-0">
                  <div className="text-sm truncate text-white">
                    {chat.title}
                  </div>
                  {chat.timestamp !== "current" && (
                    <div className="text-xs text-gray-400">
                      {chat.timestamp}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </ScrollArea>
        </div>

        {/* User Profile */}
        <div className="p-4" style={{ borderTop: "1px solid #404040" }}>
          <div className="flex items-center gap-2 mb-3">
            <Avatar className="w-8 h-8">
              <AvatarFallback className="bg-purple-600 text-white">
                SA
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <div className="text-sm font-medium text-white">Super Admin</div>
              <div className="text-xs text-gray-400 truncate">
                <EMAIL>
              </div>
            </div>
          </div>
          <Button variant="destructive" className="w-full">
            <LogOut className="w-4 h-4 mr-2" />
            Log Out
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div
        className="flex-1 flex flex-col"
        style={{ backgroundColor: "#F8F9FA" }}
      >
        {/* Header */}
        <div className="bg-white border-b border-gray-200 p-4 flex items-center justify-between">
          <h1 className="text-lg font-medium text-gray-900">
            Show me casual Nike shoes
          </h1>
          <div className="relative cursor-pointer" onClick={handleProceed}>
            <ShoppingCart className="w-6 h-6 text-gray-600" />
            {cartCount > 0 && (
              <Badge
                className="absolute -top-2 -right-2 w-5 h-5 rounded-full text-white text-xs flex items-center justify-center p-0"
                style={{
                  background:
                    "linear-gradient(180deg, #56917D 0%, #9EB998 100%)",
                }}
              >
                {cartCount}
              </Badge>
            )}
          </div>
        </div>

        {/* Chat Messages */}
        <ScrollArea
          className="flex-1 p-4"
          style={{ backgroundColor: "#F8F9FA" }}
        >
          <div className="max-w-4xl mx-auto space-y-6">
            {messages.map((message) => (
              <div key={message.id} className="space-y-4">
                {message.type === "user" ? (
                  <div className="flex justify-end">
                    <div className="flex items-start gap-2 max-w-xs">
                      <div
                        className="px-4 py-2 rounded-lg text-gray-700"
                        style={{ backgroundColor: "#E8E8E8" }}
                      >
                        {message.content}
                      </div>
                      <Avatar className="w-8 h-8">
                        <AvatarFallback className="bg-purple-600 text-white">
                          A
                        </AvatarFallback>
                      </Avatar>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="text-gray-700">{message.content}</div>

                    {message.products && (
                      <div className="space-y-4">
                        {message.products.map((product, index) => (
                          <Card
                            key={product.id}
                            className="p-4 border-0"
                            style={{
                              borderRadius: "12px",
                              background: "#FFF",
                              boxShadow: "2px 4px 34px 0 rgba(0, 0, 0, 0.05)",
                            }}
                          >
                            <CardContent className="p-0">
                              <div className="flex items-start gap-4">
                                <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center text-sm font-medium">
                                  {index + 1}
                                </div>
                                <div className="flex-1">
                                  <h3 className="font-medium text-gray-900 mb-1">
                                    {product.name}
                                  </h3>
                                  <div className="text-lg font-semibold text-gray-900 mb-3">
                                    ${product.price.toFixed(2)}
                                  </div>
                                  <Button
                                    size="sm"
                                    className="text-white hover:opacity-90 border-0"
                                    style={{
                                      background:
                                        "linear-gradient(180deg, #56917D 0%, #9EB998 100%)",
                                    }}
                                    onClick={() => handleAddToCart(product)}
                                  >
                                    Add to Cart
                                  </Button>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}

                        {/* Feedback buttons */}
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="hover:bg-gray-200"
                          >
                            <ThumbsUp className="w-4 h-4 text-gray-500" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="hover:bg-gray-200"
                          >
                            <ThumbsDown className="w-4 h-4 text-gray-500" />
                          </Button>
                        </div>
                      </div>
                    )}

                    {message.customization && (
                      <Card
                        className="p-4 border-0"
                        style={{
                          borderRadius: "12px",
                          background: "#FFF",
                          boxShadow: "2px 4px 34px 0 rgba(0, 0, 0, 0.05)",
                        }}
                      >
                        <CardContent className="p-0">
                          <div className="space-y-4">
                            <div className="space-y-3">
                              <div>
                                <h4 className="text-sm font-medium text-gray-700 mb-2">
                                  Colour:
                                </h4>
                                <div className="flex gap-2 flex-wrap">
                                  {message.customization.colors.map((color) => (
                                    <Button
                                      key={color}
                                      variant={
                                        selectedColor === color
                                          ? "default"
                                          : "outline"
                                      }
                                      size="sm"
                                      className={`rounded-full px-4 py-1 text-xs ${
                                        selectedColor === color
                                          ? "bg-gray-900 text-white hover:bg-gray-800"
                                          : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
                                      }`}
                                      onClick={() => setSelectedColor(color)}
                                    >
                                      {color}
                                    </Button>
                                  ))}
                                </div>
                              </div>

                              <div>
                                <h4 className="text-sm font-medium text-gray-700 mb-2">
                                  Size:
                                </h4>
                                <div className="flex gap-2 flex-wrap">
                                  {message.customization.sizes.map((size) => (
                                    <Button
                                      key={size}
                                      variant={
                                        selectedSize === size
                                          ? "default"
                                          : "outline"
                                      }
                                      size="sm"
                                      className={`rounded-full w-10 h-8 p-0 text-xs ${
                                        selectedSize === size
                                          ? "bg-gray-900 text-white hover:bg-gray-800"
                                          : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
                                      }`}
                                      onClick={() => setSelectedSize(size)}
                                    >
                                      {size}
                                    </Button>
                                  ))}
                                </div>
                              </div>

                              <Button
                                className="text-white hover:opacity-90 border-0"
                                style={{
                                  background:
                                    "linear-gradient(180deg, #56917D 0%, #9EB998 100%)",
                                }}
                                onClick={handleProceed}
                              >
                                Proceed
                                <Send className="w-3 h-3 ml-2" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </ScrollArea>

        {/* Input Area */}
        <div className="p-6" style={{ backgroundColor: "#F8F9FA" }}>
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center gap-3 bg-white rounded-full px-4 py-3 border border-gray-200 shadow-sm">
              <Input
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="Ask me anything about shopping..."
                className="flex-1 border-0 bg-transparent focus-visible:ring-0 text-gray-700 placeholder:text-gray-500"
                onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
              />
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 hover:bg-gray-100 rounded-full"
                >
                  <Paperclip className="w-4 h-4 text-gray-500" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 hover:bg-gray-100 rounded-full"
                >
                  <Mic className="w-4 h-4 text-gray-500" />
                </Button>
                <Button
                  size="sm"
                  className="text-white hover:opacity-90 h-8 w-8 p-0 rounded-full border-0"
                  style={{
                    background:
                      "linear-gradient(180deg, #56917D 0%, #9EB998 100%)",
                  }}
                  onClick={handleSendMessage}
                >
                  <Send className="w-3 h-3" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Off-canvas Shopping Cart */}
      {isCartVisible && (
        <>
          {/* Backdrop */}
          <div
            className={`fixed inset-0 bg-black z-40 transition-opacity duration-300 ease-in-out ${
              isCartOpen ? "opacity-50" : "opacity-0"
            }`}
            onClick={handleCloseCart}
          />

          {/* Cart Panel */}
          <div
            className={`fixed right-0 top-0 h-full w-96 bg-white z-50 shadow-xl flex flex-col transform transition-transform duration-300 ease-in-out ${
              isCartOpen ? "translate-x-0" : "translate-x-full"
            }`}
          >
            {/* Cart Header */}
            <div className="p-4 border-b border-gray-200 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <ShoppingCart className="w-5 h-5 text-gray-600" />
                <h2 className="text-lg font-semibold text-gray-900">
                  Shopping Cart
                </h2>
                <Badge className="bg-gray-100 text-gray-600 text-xs">
                  {cartCount}
                </Badge>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={handleCloseCart}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            {/* Clear Cart */}
            <div className="px-4 py-2 border-b border-gray-200">
              <Button
                variant="ghost"
                size="sm"
                className="text-red-600 hover:text-red-700 hover:bg-red-50 p-0 h-auto"
                onClick={handleClearCart}
              >
                Clear Cart
              </Button>
            </div>

            {/* Cart Items */}
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-4">
                {cartItems.map((item) => (
                  <div key={item.id} className="space-y-2">
                    <div className="font-medium text-gray-900 text-sm">
                      {item.name}
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <div>
                        <span>Color: {item.color}</span>
                        <span className="ml-4">Size: {item.size}</span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                        onClick={() => handleRemoveFromCart(item.id)}
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                    <div className="font-semibold text-gray-900">
                      ${item.price.toFixed(2)}
                    </div>
                    {item.id !== cartItems[cartItems.length - 1].id && (
                      <hr className="border-gray-200" />
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>

            {/* Cart Footer */}
            <div className="p-4 border-t border-gray-200 space-y-4">
              <div className="flex items-center justify-between font-semibold text-lg">
                <span>Total ({cartItems.length} Items)</span>
                <span>${getTotalPrice().toFixed(2)}</span>
              </div>

              <div className="space-y-2">
                <Button
                  className="w-full text-white hover:opacity-90 border-0"
                  style={{
                    background:
                      "linear-gradient(180deg, #56917D 0%, #9EB998 100%)",
                  }}
                >
                  Proceed to Checkout
                </Button>

                <Button
                  variant="outline"
                  className="w-full bg-black text-white border-black hover:bg-gray-800"
                  onClick={handleCloseCart}
                >
                  Continue Shopping
                </Button>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
